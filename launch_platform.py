#!/usr/bin/env python3
"""
DataDriven Trading Platform Launcher
Installs dependencies and launches the trading platform
"""

import sys
import os
import subprocess
import importlib.util

def check_python_version():
    """Check if we're using the correct Python version."""
    if sys.version_info < (3, 9):
        print("❌ Error: Python 3.9 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version}")
    return True

def install_package(package_name, import_name=None):
    """Install a Python package using pip."""
    if import_name is None:
        import_name = package_name
    
    # Check if package is already installed
    spec = importlib.util.find_spec(import_name)
    if spec is not None:
        print(f"✅ {package_name} is already installed")
        return True
    
    print(f"📦 Installing {package_name}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package_name}: {e}")
        return False

def install_dependencies():
    """Install all required dependencies."""
    print("🔧 Installing Dependencies...")
    print("=" * 50)
    
    # Required packages
    required_packages = [
        ("PyQt6", "PyQt6"),
        ("PyQtGraph", "pyqtgraph"),
        ("numpy", "numpy"),
        ("pandas", "pandas"),
        ("yfinance", "yfinance"),
        ("aiohttp", "aiohttp"),
        ("asyncio-mqtt", "asyncio_mqtt"),
        ("psutil", "psutil"),
        ("structlog", "structlog"),
        ("prometheus-client", "prometheus_client"),
        ("protobuf", "google.protobuf"),
        ("pydantic", "pydantic"),
        ("click", "click"),
        ("rich", "rich"),
    ]
    
    failed_packages = []
    
    for package_name, import_name in required_packages:
        if not install_package(package_name, import_name):
            failed_packages.append(package_name)
    
    if failed_packages:
        print(f"\n❌ Failed to install: {', '.join(failed_packages)}")
        return False
    
    print("\n✅ All dependencies installed successfully!")
    return True

def setup_python_path():
    """Setup Python path to include the indicators module."""
    # Add the Debug build directory to Python path
    debug_path = os.path.join(os.getcwd(), 'build', 'Debug')
    if debug_path not in sys.path:
        sys.path.insert(0, debug_path)
    
    # Add src directory to Python path
    src_path = os.path.join(os.getcwd(), 'src')
    if src_path not in sys.path:
        sys.path.insert(0, src_path)
    
    print(f"✅ Python path configured:")
    print(f"   - Indicators: {debug_path}")
    print(f"   - Source: {src_path}")

def verify_indicators_module():
    """Verify that the indicators module can be imported."""
    try:
        import indicators
        build_info = indicators.get_build_info()
        print(f"✅ Indicators module loaded successfully")
        print(f"   - Version: {build_info.get('version', 'Unknown')}")
        print(f"   - Build type: {build_info.get('build_type', 'Unknown')}")
        print(f"   - SIMD enabled: {build_info.get('simd_enabled', False)}")
        print(f"   - Functions: {len([x for x in dir(indicators) if not x.startswith('_')])}")
        return True
    except ImportError as e:
        print(f"❌ Failed to import indicators module: {e}")
        return False

def launch_platform():
    """Launch the DataDriven Trading Platform."""
    print("\n🚀 Launching DataDriven Trading Platform...")
    print("=" * 50)
    
    try:
        # Import and run the main application
        from main import TradingApplication
        
        print("✅ Main application imported successfully")
        print("🎯 Starting trading platform...")
        
        app = TradingApplication()
        return app.run()
        
    except ImportError as e:
        print(f"❌ Failed to import main application: {e}")
        print("Make sure you're running from the correct directory.")
        return 1
    except Exception as e:
        print(f"❌ Application error: {e}")
        return 1

def main():
    """Main launcher function."""
    print("🎉 DataDriven Trading Platform Launcher")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Dependency installation failed. Please install manually.")
        return 1
    
    # Setup Python path
    setup_python_path()
    
    # Verify indicators module
    if not verify_indicators_module():
        print("\n❌ Indicators module verification failed.")
        print("Make sure the C++ module was built successfully.")
        return 1
    
    # Launch the platform
    return launch_platform()

if __name__ == "__main__":
    sys.exit(main())
