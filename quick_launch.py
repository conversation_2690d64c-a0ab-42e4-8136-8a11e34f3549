#!/usr/bin/env python3
"""
Quick Launch for DataDriven Trading Platform
Installs only essential dependencies and launches the platform
"""

import sys
import os
import subprocess

def install_essential_packages():
    """Install only the essential packages needed to run the platform."""
    print("🔧 Installing Essential Dependencies...")
    print("=" * 50)
    
    essential_packages = [
        "PyQt6",
        "PyQtGraph", 
        "aiohttp",
        "psutil",
        "structlog",
        "pydantic",
        "rich"
    ]
    
    for package in essential_packages:
        print(f"📦 Installing {package}...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--quiet"
            ])
            print(f"✅ {package} installed")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    print("\n✅ Essential dependencies installed!")
    return True

def setup_environment():
    """Setup the environment for the platform."""
    # Add paths
    debug_path = os.path.join(os.getcwd(), 'build', 'Debug')
    src_path = os.path.join(os.getcwd(), 'src')
    
    if debug_path not in sys.path:
        sys.path.insert(0, debug_path)
    if src_path not in sys.path:
        sys.path.insert(0, src_path)
    
    print(f"✅ Environment configured")

def main():
    """Quick launch main function."""
    print("🚀 DataDriven Trading Platform - Quick Launch")
    print("=" * 55)
    
    # Install essential packages
    if not install_essential_packages():
        print("❌ Failed to install dependencies")
        return 1
    
    # Setup environment
    setup_environment()
    
    # Test indicators module
    try:
        import indicators
        print("✅ Indicators module loaded")
    except ImportError as e:
        print(f"❌ Failed to load indicators: {e}")
        return 1
    
    # Launch platform
    print("\n🎯 Launching Platform...")
    try:
        from main import TradingApplication
        app = TradingApplication()
        return app.run()
    except Exception as e:
        print(f"❌ Launch failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
