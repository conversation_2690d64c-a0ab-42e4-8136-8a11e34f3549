@echo off
echo DataDriven Trading Platform - MSVC Environment Setup
echo =====================================================

REM Set up MSVC environment
echo Setting up Visual Studio 2022 Build Tools environment...
call "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat"

if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to set up MSVC environment
    echo Please ensure Visual Studio 2022 Build Tools are installed
    pause
    exit /b 1
)

echo.
echo Adding Qt6 to PATH...
set PATH=C:\Qt\6.9.1\mingw_64\bin;%PATH%

echo.
echo Verifying environment setup...
echo Checking MSVC libraries...
if "%LIB%"=="" (
    echo ERROR: MSVC environment not properly set up
    pause
    exit /b 1
) else (
    echo [OK] MSVC libraries found
)

echo Checking Qt6...
qmake -v >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Qt6 not found in PATH
    echo Please ensure Qt6 is installed at C:\Qt\6.9.1\mingw_64\
    pause
    exit /b 1
) else (
    echo [OK] Qt6 found
)

echo [OK] Windows SDK tools available via MSVC environment

echo.
echo =====================================================
echo Environment setup complete!
echo =====================================================
echo.
echo RECOMMENDED: Manual build commands (avoids linker issues):
echo   1. mkdir build ^&^& cd build
echo   2. cmake .. -DCMAKE_BUILD_TYPE=Release -DBUILD_PYTHON_BINDINGS=ON -DBUILD_TESTS=ON -DBUILD_BENCHMARKS=ON -DENABLE_SIMD=ON -DCMAKE_INTERPROCEDURAL_OPTIMIZATION=OFF -G "Visual Studio 17 2022" -A x64
echo   3. cmake --build . --config Release
echo.
echo ALTERNATIVE: Automated script (may have linker issues):
echo   python setup_development.py build_ext --inplace --force
echo.
echo Opening VS Code with proper environment...
code .

echo.
echo Environment is ready. Use the manual build commands above for best results.
echo Press any key to exit...
pause >nul
