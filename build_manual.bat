@echo off
echo DataDriven Trading Platform - Manual Build Script
echo ================================================

REM Set up MSVC environment
echo Setting up Visual Studio 2022 Build Tools environment...
call "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat"

if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to set up MSVC environment
    echo Please ensure Visual Studio 2022 Build Tools are installed
    pause
    exit /b 1
)

echo.
echo Adding Qt6 to PATH...
set PATH=C:\Qt\6.9.1\mingw_64\bin;%PATH%

echo.
echo Cleaning and creating build directory...
if exist build rmdir /s /q build
mkdir build
cd build

echo.
echo Configuring CMake with Visual Studio generator (LTO disabled)...
cmake .. ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DBUILD_PYTHON_BINDINGS=ON ^
    -DBUILD_TESTS=ON ^
    -DBUILD_BENCHMARKS=ON ^
    -DENABLE_SIMD=ON ^
    -DENABLE_LTO=OFF ^
    -DCMAKE_CXX_FLAGS_RELEASE="/O2 /DNDEBUG" ^
    -DCMAKE_C_FLAGS_RELEASE="/O2 /DNDEBUG" ^
    -G "Visual Studio 17 2022" ^
    -A x64

if %ERRORLEVEL% neq 0 (
    echo ERROR: CMake configuration failed
    cd ..
    pause
    exit /b 1
)

echo.
echo Building project...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed
    cd ..
    pause
    exit /b 1
)

echo.
echo ================================================
echo Build completed successfully!
echo ================================================
echo.
echo Built files are in the build/Release directory.
echo.

cd ..
pause
