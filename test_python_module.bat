@echo off
echo Testing Python Module Import...
echo.

echo Using Python 3.13:
"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" -c "import sys; sys.path.insert(0, 'build/Debug'); import indicators; print('SUCCESS: indicators module imported!'); print('Available functions:', len([x for x in dir(indicators) if not x.startswith('_')]))"

echo.
echo Using default Python:
python -c "import sys; print('Python version:', sys.version)"

pause
